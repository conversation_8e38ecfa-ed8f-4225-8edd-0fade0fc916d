import { BaseModel } from '@apply-goal-backend/database';
import {
  BelongsTo,
  Column,
  DataType,
  ForeignKey,
  HasMany,
  HasOne,
  PrimaryKey,
  Table,
} from 'sequelize-typescript';
import { User } from '../../user/model/user.model';
import { EmployeeAddress } from './employee-address.model';
import { EmployeeEmergencyContact } from './employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './employee-identity-doc.model';
import { EmployeeBankAccount } from './employee-bank-account.model';
import { EmployeeSocialLinks } from './employee-social-links.model';
import { EmployeeDepartment } from './employee-department.model';
import { Department } from '../../user/model/department.model';

@Table({ tableName: 'employee_personal' })
export class EmployeePersonal extends BaseModel {
  @PrimaryKey
  @ForeignKey(() => User)
  @Column(DataType.BIGINT)
  userId!: number;

  // Personal Information
  @Column(DataType.STRING(100))
  lastName?: string;

  @Column(DataType.STRING(100))
  firstName?: string;

  @Column(DataType.STRING(20))
  phone?: string;

  @Column(DataType.DATE)
  dateOfBirth?: Date;

  @Column(DataType.STRING(10))
  bloodGroup?: string;

  @Column(DataType.STRING(10))
  gender?: string;

  @Column(DataType.STRING(100))
  nationality?: string;

  @Column(DataType.STRING(50))
  maritalStatus?: string;

  //  employment info
  @Column(DataType.DATE)
  joiningDate?: Date;

  @Column(DataType.STRING(50))
  jobType?: string;

  @Column(DataType.STRING(50))
  jobStatus?: string;

  @Column(DataType.STRING(50))
  employeeId?: string;

  // Legacy fields
  @Column(DataType.BIGINT)
  agencyId?: number;

  @Column(DataType.STRING(100))
  orgId?: string;

  @HasMany(() => EmployeeAddress)
  addresses?: EmployeeAddress[];

  @HasMany(() => EmployeeEmergencyContact)
  emergencyContacts?: EmployeeEmergencyContact[];

  @HasMany(() => EmployeeIdentityDoc)
  identityDocs?: EmployeeIdentityDoc[];

  @HasMany(() => EmployeeBankAccount)
  bankAccounts?: EmployeeBankAccount[];

  @HasMany(() => EmployeeSocialLinks)
  socialLinks?: EmployeeSocialLinks[];

  @HasOne(() => EmployeeDepartment)
  employeeDepartment?: EmployeeDepartment;

  @BelongsTo(() => Department)
  department?: Department;
}
