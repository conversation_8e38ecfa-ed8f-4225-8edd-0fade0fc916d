import {
  Injectable,
  NotFoundException,
  Logger,
  BadRequestException,
} from '@nestjs/common';
import { InjectModel } from '@nestjs/sequelize';
import { Op, UniqueConstraintError } from 'sequelize';
import { firstValueFrom } from 'rxjs';
import { EmployeePersonal } from './models/employee-personal.model';
import { EmployeeAddress } from './models/employee-address.model';
import { EmployeeEmergencyContact } from './models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from './models/employee-identity-doc.model';
import { EmployeeBankAccount } from './models/employee-bank-account.model';
import { CreateEmployeeDto } from './dto/create-employee.dto';
import { UpdateEmployeeDto } from './dto/update-employee.dto';
import { AuditClientService } from '../audit/audit.service';
import { AppService } from '../app.service';
import {
  CreateEmployeeRequest,
  UpdateEmployeeRequest,
  GetEmployeeRequest,
  ListEmployeesRequest,
  RemoveEmployeeRequest,
  EmployeeResponse,
  ListEmployeesResponse,
  RemoveEmployeeResponse,
  EmployeeInfo,
  CreateEmployeeData,
  UpdateEmployeeData,
  EmployeeAddressInfo,
  EmployeeEmergencyContactInfo,
  EmployeeIdentityDocInfo,
  EmployeeBankAccountInfo,
  JOB_TYPES,
  JOB_STATUSES,
  BLOOD_GROUPS,
  GENDERS,
} from './employee.interface';
import { CreateAuditLogRequest } from '../auth/auth.interfaces';
import { User } from '../user/model/user.model';
import { Role } from '../user/model/role.model';
import { UserRole } from '../user/model/user-role.model';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { Organization } from '../organization/organization.model';
import * as bcrypt from 'bcrypt';
import { EmployeeSocialLinks } from './models/employee-social-links.model';
import { EmployeeDepartment } from './models/employee-department.model';

@Injectable()
export class EmployeeService {
  private readonly logger = new Logger(EmployeeService.name);

  constructor(
    @InjectModel(EmployeePersonal)
    private readonly employeeModel: typeof EmployeePersonal,
    @InjectModel(EmployeeAddress)
    private readonly employeeAddressModel: typeof EmployeeAddress,
    @InjectModel(EmployeeEmergencyContact)
    private readonly employeeEmergencyContactModel: typeof EmployeeEmergencyContact,
    @InjectModel(EmployeeIdentityDoc)
    private readonly employeeIdentityDocModel: typeof EmployeeIdentityDoc,
    @InjectModel(EmployeeBankAccount)
    private readonly employeeBankAccountModel: typeof EmployeeBankAccount,
    @InjectModel(EmployeeSocialLinks)
    private readonly employeeSocialLinksModel: typeof EmployeeSocialLinks,
    @InjectModel(User)
    private readonly userModel: typeof User,
    @InjectModel(Role)
    private readonly roleModel: typeof Role,
    @InjectModel(UserRole)
    private readonly userRoleModel: typeof UserRole,
    @InjectModel(Department)
    private readonly departmentModel: typeof Department,
    @InjectModel(UserDepartment)
    private readonly userDepartmentModel: typeof UserDepartment,
    @InjectModel(Organization)
    private readonly organizationModel: typeof Organization,
    @InjectModel(EmployeeDepartment)
    private readonly employeeDepartmentModel: typeof EmployeeDepartment,
    private readonly auditService: AuditClientService,
    private readonly appService: AppService
  ) {}

  // Audit-log helper, mirroring AuthService
  private async createAuditLog(payload: CreateAuditLogRequest) {
    const start = Date.now();
    try {
      await firstValueFrom(this.auditService.createAuditLog(payload));
      this.appService.trackAuditLogRequest(
        'create',
        'success',
        (Date.now() - start) / 1000
      );
    } catch (error) {
      this.appService.trackAuditLogRequest(
        'create',
        'error',
        (Date.now() - start) / 1000
      );
      this.logger.error('Failed to create audit log', error);
    }
  }

  // Metrics tracking helper
  private trackMetrics(
    operation: string,
    status: 'success' | 'failure' | 'error',
    duration: number
  ) {
    try {
      this.appService.trackAuthorization('employee', operation, status);
    } catch (err) {
      this.logger.warn(`Metrics error: ${err.message}`);
    }
  }

  async createEmployee(
    request: CreateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    let transaction: any;

    try {
      // Log the full request for debugging
      this.logger.debug(
        `CreateEmployee request received: ${JSON.stringify(request, null, 2)}`
      );

      // Validate required fields
      if (!request.email) {
        this.logger.error('Email is required but not provided in request');
        this.logger.error(`Request keys: ${Object.keys(request).join(', ')}`);
        return {
          success: false,
          message: 'Email is required',
        };
      }

      this.logger.log(`Creating employee with email: ${request.email}`);

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // 1. Check if email already exists
      const existingUser = await this.userModel.findOne({
        where: { email: request.email },
        transaction,
      });

      if (existingUser) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Email already exists',
        };
      }

      // 2. Validate job type and status
      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      // 3. Validate organization exists
      const organization = await this.organizationModel.findByPk(
        request.organizationId,
        { transaction }
      );

      if (!organization) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Organization not found',
        };
      }

      // 4. Find default employee role for the organization
      let role = await this.roleModel.findOne({
        where: {
          name: 'Employee',
          organizationId: organization.id,
        },
        transaction,
      });

      // If not found, try to find system role
      if (!role) {
        role = await this.roleModel.findOne({
          where: {
            name: 'Employee',
            isSystemRole: true,
            organizationId: null,
          },
          transaction,
        });
      }

      // If still not found, create organization-specific role
      if (!role) {
        this.logger.log(
          `Creating Employee role for organization '${organization.name}'`
        );
        role = await this.roleModel.create(
          {
            name: 'Employee',
            organizationId: organization.id,
            description: `Employee role for ${organization.name}`,
            isSystemRole: false,
          },
          { transaction }
        );
      }

      // 5. Create user
      const hashedPassword = request.password
        ? await bcrypt.hash(request.password, 10)
        : await bcrypt.hash('DefaultPassword123!', 10); // Default password

      const user = await this.userModel.create(
        {
          email: request.email,
          password: hashedPassword,
          name: `${request.firstName} ${request.lastName}`.trim(),
          phone: request.phone,
          status: 'active',
          organizationId: organization.id,
        },
        { transaction }
      );

      // 6. Assign role to user
      await this.userRoleModel.create(
        {
          userId: user.id,
          roleId: role.id,
        },
        { transaction }
      );

      // 7. Create department assignment if department info provided
      if (request.departmentInfo?.department) {
        // Find the departmentId from the Department model
        let department = await this.departmentModel.findOne({
          where: {
            name: request.departmentInfo.department,
            organizationId: organization.id,
          },
          transaction,
        });
        if (!department) {
          department = await this.departmentModel.create(
            {
              name: request.departmentInfo.department,
              organizationId: organization.id,
              description: `${request.departmentInfo.department} department`,
            },
            { transaction }
          );
        }
        await this.employeeDepartmentModel.create(
          {
            userId: user.id,
            departmentId: department.id,
            employeeId: request.departmentInfo.employeeId,
            department: request.departmentInfo.department,
            designation: request.departmentInfo.designation,
            supervisor: request.departmentInfo.supervisor,
            workLocation: request.departmentInfo.workLocation,
          },
          { transaction }
        );
      }

      // 8. Create employee record with structured data
      const employeeData: CreateEmployeeData = {
        userId: BigInt(user.id),

        // Personal Information
        lastName: request.lastName,
        firstName: request.firstName,
        phone: request.phone,
        dateOfBirth: request.dateOfBirth
          ? new Date(request.dateOfBirth)
          : undefined,
        bloodGroup: request.bloodGroup,
        gender: request.gender,
        nationality: request.nationality,
        maritalStatus: request.maritalStatus,

        // Employment Information
        joiningDate: request.joiningDate
          ? new Date(request.joiningDate)
          : new Date(), // Default to today
        jobType: request.jobType || 'full-time',
        jobStatus: request.jobStatus || 'active',

        // Note: Department Information is stored separately in EmployeeDepartment table
        // Note: Address, Emergency Contact, Bank Account, and Social Links are stored in separate tables

        // Legacy fields
        agencyId: request.organizationId,
        orgId: request.organizationId.toString(),

        // Required field for interface compliance
        socialLinks: [],
      };

      const employee = await this.employeeModel.create(employeeData as any, {
        transaction,
      });

      // 11. Commit transaction before creating related records
      await transaction.commit();

      // 12. Create related records from both nested structure and legacy arrays
      await this.createRelatedEmployeeRecords(BigInt(employee.userId), request);

      // Audit-log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'CREATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Created employee for user: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee created successfully: ${employee.userId}`);

      // Fetch the complete employee with specific relations
      const fullEmployee = await this.employeeModel.findByPk(employee.userId, {
        include: [
          { model: this.employeeAddressModel, as: 'addresses' },
          {
            model: this.employeeEmergencyContactModel,
            as: 'emergencyContacts',
          },
          { model: this.employeeIdentityDocModel, as: 'identityDocs' },
          { model: this.employeeBankAccountModel, as: 'bankAccounts' },
          { model: this.employeeSocialLinksModel, as: 'socialLinks' },
          {
            model: this.employeeDepartmentModel,
            as: 'employeeDepartment',
            include: [{ model: this.departmentModel, as: 'departmentRef' }],
          },
        ],
      });

      if (!fullEmployee) {
        this.logger.error(
          `Failed to fetch created employee: ${employee.userId}`
        );
        throw new Error('Employee created but could not be retrieved');
      }

      // Metrics
      this.trackMetrics('create_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee created successfully',
        employee: await this.mapToEmployeeInfo(fullEmployee),
      };
    } catch (error) {
      // Rollback transaction if it exists and hasn't been committed
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Failed to rollback transaction', rollbackError);
        }
      }

      this.logger.error(
        `Error creating employee: ${error.message}`,
        error.stack
      );
      this.trackMetrics('create_employee', 'error', Date.now() - startTime);

      if (error instanceof BadRequestException) {
        throw error;
      }

      throw new Error('Failed to create employee');
    }
  }

  private async createRelatedEmployeeRecords(
    userId: bigint,
    request: CreateEmployeeRequest
  ): Promise<void> {
    // Create address records from structured data
    const addressRecords = [];

    // Present address
    if (request.presentAddress?.presentAddress) {
      addressRecords.push({
        userId: Number(userId),
        addressType: 'present',
        addressLine: request.presentAddress.presentAddress,
        country: request.presentAddress.presentCountry,
        state: request.presentAddress.presentState,
        city: request.presentAddress.presentCity,
        postalCode: request.presentAddress.presentPostalCode,
      });
    }

    // Permanent address
    if (request.permanentAddress?.permanentAddress) {
      addressRecords.push({
        userId: Number(userId),
        addressType: 'permanent',
        addressLine: request.permanentAddress.permanentAddress,
        country: request.permanentAddress.permanentCountry,
        state: request.permanentAddress.permanentState,
        city: request.permanentAddress.permanentCity,
        postalCode: request.permanentAddress.permanentPostalCode,
      });
    }

    if (addressRecords.length > 0) {
      await this.employeeAddressModel.bulkCreate(addressRecords);
    }

    // Create emergency contact records
    const emergencyContactRecords = [];

    // From structured data
    if (request.emergencyContact?.emergencyContactName) {
      emergencyContactRecords.push({
        userId: Number(userId),
        category: request.emergencyContact.emergencyContactType || 'Primary',
        name: request.emergencyContact.emergencyContactName,
        relationship: request.emergencyContact.emergencyContactRelation,
        address: request.emergencyContact.emergencyContactAddress,
        phoneNumber: request.emergencyContact.emergencyContactPhone,
        email: request.emergencyContact.emergencyContactEmail,
      });
    }

    if (emergencyContactRecords.length > 0) {
      await this.employeeEmergencyContactModel.bulkCreate(
        emergencyContactRecords
      );
    }

    // Create identity document records
    const identityDocRecords = [];

    // From structured data (array)
    if (request.identityInfo?.length) {
      const identityDocs = request.identityInfo.map((identity) => ({
        userId: Number(userId),
        docType: identity.docType,
        number: identity.number,
        nationality: identity.nationality,
        issueDate: identity.issueDate ? new Date(identity.issueDate) : null,
        expiryDate: identity.expiryDate ? new Date(identity.expiryDate) : null,
      }));
      identityDocRecords.push(...identityDocs);
    }

    if (identityDocRecords.length > 0) {
      await this.employeeIdentityDocModel.bulkCreate(identityDocRecords);
    }

    // Create bank account records
    const bankAccountRecords = [];

    // From structured data
    if (request.bankAccount?.accountHolderName) {
      bankAccountRecords.push({
        userId: Number(userId),
        accountHolder: request.bankAccount.accountHolderName,
        accountNumber: request.bankAccount.accountNumber,
        bankName: request.bankAccount.bankName,
        branchName: request.bankAccount.branchName,
      });
    }

    if (bankAccountRecords.length > 0) {
      await this.employeeBankAccountModel.bulkCreate(bankAccountRecords);
    }

    // Create social links records
    if (request.socialLinks?.length) {
      const socialLinksRecords = request.socialLinks.map((link) => ({
        userId: Number(userId),
        platform: link.platform,
        url: link.url,
      }));
      this.logger.debug(
        `Creating social links records: ${JSON.stringify(
          socialLinksRecords,
          null,
          2
        )}`
      );
      await this.employeeSocialLinksModel.bulkCreate(socialLinksRecords);
    }
  }

  private async updateRelatedEmployeeRecords(
    userId: bigint,
    request: UpdateEmployeeRequest
  ): Promise<void> {
    // For updates, we'll replace existing records with new ones if data is provided

    // Update address records if address data is provided
    const hasAddressData =
      request.presentAddress?.presentAddress ||
      request.permanentAddress?.permanentAddress;

    if (hasAddressData) {
      // Delete existing addresses
      await this.employeeAddressModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new address records
      const addressRecords = [];

      // Present address
      if (request.presentAddress?.presentAddress) {
        addressRecords.push({
          userId: Number(userId),
          addressType: 'present',
          addressLine: request.presentAddress.presentAddress,
          country: request.presentAddress.presentCountry,
          state: request.presentAddress.presentState,
          city: request.presentAddress.presentCity,
          postalCode: request.presentAddress.presentPostalCode,
        });
      }

      // Permanent address
      if (request.permanentAddress?.permanentAddress) {
        addressRecords.push({
          userId: Number(userId),
          addressType: 'permanent',
          addressLine: request.permanentAddress.permanentAddress,
          country: request.permanentAddress.permanentCountry,
          state: request.permanentAddress.permanentState,
          city: request.permanentAddress.permanentCity,
          postalCode: request.permanentAddress.permanentPostalCode,
        });
      }

      if (addressRecords.length > 0) {
        await this.employeeAddressModel.bulkCreate(addressRecords);
      }
    }

    // Update emergency contact records if contact data is provided
    const hasEmergencyContactData =
      request.emergencyContact?.emergencyContactName;

    if (hasEmergencyContactData) {
      // Delete existing emergency contacts
      await this.employeeEmergencyContactModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new emergency contact records
      const emergencyContactRecords = [];

      // From structured data
      if (request.emergencyContact?.emergencyContactName) {
        emergencyContactRecords.push({
          userId: Number(userId),
          category: request.emergencyContact.emergencyContactType || 'Primary',
          name: request.emergencyContact.emergencyContactName,
          relationship: request.emergencyContact.emergencyContactRelation,
          address: request.emergencyContact.emergencyContactAddress,
          phoneNumber: request.emergencyContact.emergencyContactPhone,
          email: request.emergencyContact.emergencyContactEmail,
        });
      }

      if (emergencyContactRecords.length > 0) {
        await this.employeeEmergencyContactModel.bulkCreate(
          emergencyContactRecords
        );
      }
    }

    // Update identity document records if identity data is provided
    const hasIdentityData = request.identityInfo?.length;

    if (hasIdentityData) {
      // Delete existing identity docs
      await this.employeeIdentityDocModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new identity document records
      const identityDocRecords = [];

      // From structured data (array)
      if (request.identityInfo?.length) {
        const identityDocs = request.identityInfo.map((identity) => ({
          userId: Number(userId),
          docType: identity.docType,
          number: identity.number,
          nationality: identity.nationality,
          issueDate: identity.issueDate ? new Date(identity.issueDate) : null,
          expiryDate: identity.expiryDate
            ? new Date(identity.expiryDate)
            : null,
        }));
        identityDocRecords.push(...identityDocs);
      }

      if (identityDocRecords.length > 0) {
        await this.employeeIdentityDocModel.bulkCreate(identityDocRecords);
      }
    }

    // Update bank account records if bank data is provided
    const hasBankAccountData = request.bankAccount?.accountHolderName;

    if (hasBankAccountData) {
      // Delete existing bank accounts
      await this.employeeBankAccountModel.destroy({
        where: { userId: Number(userId) },
      });

      // Create new bank account records
      const bankAccountRecords = [];

      // From structured data
      if (request.bankAccount?.accountHolderName) {
        bankAccountRecords.push({
          userId: Number(userId),
          accountHolder: request.bankAccount.accountHolderName,
          accountNumber: request.bankAccount.accountNumber,
          bankName: request.bankAccount.bankName,
          branchName: request.bankAccount.branchName,
        });
      }

      if (bankAccountRecords.length > 0) {
        await this.employeeBankAccountModel.bulkCreate(bankAccountRecords);
      }
    }

    // Update social links records if socialLinks data is provided
    if (request.socialLinks?.length) {
      // Delete existing social links
      await this.employeeSocialLinksModel.destroy({
        where: { userId: Number(userId) },
      });
      // Create new social links records
      const socialLinksRecords = request.socialLinks.map((link) => ({
        userId: Number(userId),
        platform: link.platform,
        url: link.url,
      }));
      await this.employeeSocialLinksModel.bulkCreate(socialLinksRecords);
    }
  }

  async mapToEmployeeInfo(employee: EmployeePersonal): Promise<EmployeeInfo> {
    let fullEmployee = employee;
    if (
      !employee.addresses &&
      !employee.emergencyContacts &&
      !employee.identityDocs &&
      !employee.bankAccounts &&
      !(employee as any).socialLinks &&
      !(employee as any).employeeDepartment
    ) {
      const fetchedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: [
            { model: this.employeeAddressModel, as: 'addresses' },
            {
              model: this.employeeEmergencyContactModel,
              as: 'emergencyContacts',
            },
            { model: this.employeeIdentityDocModel, as: 'identityDocs' },
            { model: this.employeeBankAccountModel, as: 'bankAccounts' },
            { model: this.employeeSocialLinksModel, as: 'socialLinks' },
            {
              model: this.employeeDepartmentModel,
              as: 'employeeDepartment',
              include: [{ model: this.departmentModel, as: 'departmentRef' }],
            },
          ],
        }
      );
      if (!fetchedEmployee) {
        throw new NotFoundException(
          `Employee not found with userId: ${employee.userId}`
        );
      }
      fullEmployee = fetchedEmployee;
    }

    // Fetch social links if not loaded
    let socialLinks = (fullEmployee as any).socialLinks;
    if (!socialLinks) {
      socialLinks = await this.employeeSocialLinksModel.findAll({
        where: { userId: fullEmployee.userId },
      });
    }

    // Fetch identity docs if not loaded
    let identityDocs = (fullEmployee as any).identityDocs;
    if (!identityDocs) {
      identityDocs = await this.employeeIdentityDocModel.findAll({
        where: { userId: fullEmployee.userId },
      });
    }

    // Fetch employee department info if not loaded
    let employeeDepartment = (fullEmployee as any).employeeDepartment;
    if (!employeeDepartment) {
      employeeDepartment = await this.employeeDepartmentModel.findOne({
        where: { userId: fullEmployee.userId },
        include: [{ model: this.departmentModel, as: 'departmentRef' }],
      });
    }

    return {
      userId: BigInt(fullEmployee.userId),
      lastName: fullEmployee.lastName,
      firstName: fullEmployee.firstName,
      phone: fullEmployee.phone,
      dateOfBirth: fullEmployee.dateOfBirth?.toISOString(),
      bloodGroup: fullEmployee.bloodGroup,
      gender: fullEmployee.gender,
      nationality: fullEmployee.nationality,
      maritalStatus: fullEmployee.maritalStatus,

      // Employment Information
      joiningDate: fullEmployee.joiningDate?.toISOString(),
      jobType: fullEmployee.jobType,
      jobStatus: fullEmployee.jobStatus,

      // Department Information from EmployeeDepartment table
      employeeId: employeeDepartment?.employeeId,
      department: employeeDepartment?.department,
      designation: employeeDepartment?.designation,
      supervisor: employeeDepartment?.supervisor,
      workLocation: employeeDepartment?.workLocation,

      identityInfo: identityDocs,
      socialLinks: socialLinks,
      organizationId: (fullEmployee as any).user?.organizationId || undefined,
      createdAt: fullEmployee.createdAt,
      updatedAt: fullEmployee.updatedAt,
    };
  }

  // Helper method to get user's organization ID
  private async getUserOrganizationId(
    userId: bigint
  ): Promise<bigint | undefined> {
    try {
      const user = await this.userModel.findByPk(Number(userId));
      return user?.organizationId ? BigInt(user.organizationId) : undefined;
    } catch (error) {
      this.logger.error(
        `Error getting organization ID for user ${userId}:`,
        error
      );
      return undefined;
    }
  }

  // New updateEmployee method that properly handles the UpdateEmployeeRequest
  async updateEmployee(
    request: UpdateEmployeeRequest
  ): Promise<EmployeeResponse> {
    const startTime = Date.now();
    let transaction;

    try {
      this.logger.log(`Updating employee: ${request.id}`);

      // Start transaction
      transaction = await this.userModel.sequelize.transaction();

      // 1. Find the employee
      const employee = await this.employeeModel.findByPk(Number(request.id), {
        include: [
          { model: this.employeeAddressModel, as: 'addresses' },
          {
            model: this.employeeEmergencyContactModel,
            as: 'emergencyContacts',
          },
          { model: this.employeeIdentityDocModel, as: 'identityDocs' },
          { model: this.employeeBankAccountModel, as: 'bankAccounts' },
          { model: this.employeeSocialLinksModel, as: 'socialLinks' },
          {
            model: this.employeeDepartmentModel,
            as: 'employeeDepartment',
            include: [{ model: this.departmentModel, as: 'departmentRef' }],
          },
        ],
        transaction,
      });

      if (!employee) {
        await transaction.rollback();
        return {
          success: false,
          message: 'Employee not found',
        };
      }

      // 2. Validate job type and status if provided
      if (request.jobType && !JOB_TYPES.includes(request.jobType as any)) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job type. Must be one of: ${JOB_TYPES.join(', ')}`
        );
      }
      if (
        request.jobStatus &&
        !JOB_STATUSES.includes(request.jobStatus as any)
      ) {
        await transaction.rollback();
        throw new BadRequestException(
          `Invalid job status. Must be one of: ${JOB_STATUSES.join(', ')}`
        );
      }

      // 3. Prepare update data with comprehensive mapping
      const updateData: Partial<UpdateEmployeeData> = {};

      // Personal Information
      if (request.firstName !== undefined)
        updateData.firstName = request.firstName;
      if (request.lastName !== undefined)
        updateData.lastName = request.lastName;
      if (request.email !== undefined) updateData.email = request.email;
      if (request.phone !== undefined) updateData.phone = request.phone;
      if (request.bloodGroup !== undefined)
        updateData.bloodGroup = request.bloodGroup;
      if (request.gender !== undefined) updateData.gender = request.gender;
      if (request.nationality !== undefined)
        updateData.nationality = request.nationality;
      if (request.maritalStatus !== undefined)
        updateData.maritalStatus = request.maritalStatus;

      // Employment Information
      if (request.jobType !== undefined) updateData.jobType = request.jobType;
      if (request.jobStatus !== undefined)
        updateData.jobStatus = request.jobStatus;

      // Note: Department Information will be handled separately in EmployeeDepartment table

      // Address Information
      if (request.presentAddress?.presentAddress !== undefined)
        updateData.presentAddress = request.presentAddress.presentAddress;
      if (request.presentAddress?.presentCountry !== undefined)
        updateData.presentCountry = request.presentAddress.presentCountry;
      if (request.presentAddress?.presentState !== undefined)
        updateData.presentState = request.presentAddress.presentState;
      if (request.presentAddress?.presentCity !== undefined)
        updateData.presentCity = request.presentAddress.presentCity;
      if (request.presentAddress?.presentPostalCode !== undefined)
        updateData.presentPostalCode = request.presentAddress.presentPostalCode;

      if (request.permanentAddress?.permanentAddress !== undefined)
        updateData.permanentAddress = request.permanentAddress.permanentAddress;
      if (request.permanentAddress?.permanentCountry !== undefined)
        updateData.permanentCountry = request.permanentAddress.permanentCountry;
      if (request.permanentAddress?.permanentState !== undefined)
        updateData.permanentState = request.permanentAddress.permanentState;
      if (request.permanentAddress?.permanentCity !== undefined)
        updateData.permanentCity = request.permanentAddress.permanentCity;
      if (request.permanentAddress?.permanentPostalCode !== undefined)
        updateData.permanentPostalCode =
          request.permanentAddress.permanentPostalCode;

      // Emergency Contact
      if (request.emergencyContact?.emergencyContactType !== undefined)
        updateData.emergencyContactType =
          request.emergencyContact.emergencyContactType;
      if (request.emergencyContact?.emergencyContactName !== undefined)
        updateData.emergencyContactName =
          request.emergencyContact.emergencyContactName;
      if (request.emergencyContact?.emergencyContactRelation !== undefined)
        updateData.emergencyContactRelation =
          request.emergencyContact.emergencyContactRelation;
      if (request.emergencyContact?.emergencyContactPhone !== undefined)
        updateData.emergencyContactPhone =
          request.emergencyContact.emergencyContactPhone;
      if (request.emergencyContact?.emergencyContactEmail !== undefined)
        updateData.emergencyContactEmail =
          request.emergencyContact.emergencyContactEmail;
      if (request.emergencyContact?.emergencyContactAddress !== undefined)
        updateData.emergencyContactAddress =
          request.emergencyContact.emergencyContactAddress;

      // Handle date fields with proper validation
      if (request.joiningDate !== undefined) {
        if (request.joiningDate) {
          const joiningDate = new Date(request.joiningDate);
          if (isNaN(joiningDate.getTime())) {
            await transaction.rollback();
            throw new BadRequestException('Invalid joiningDate format');
          }
          updateData.joiningDate = joiningDate;
        } else {
          updateData.joiningDate = null;
        }
      }

      if (request.dateOfBirth !== undefined) {
        if (request.dateOfBirth) {
          const dateOfBirth = new Date(request.dateOfBirth);
          if (isNaN(dateOfBirth.getTime())) {
            await transaction.rollback();
            throw new BadRequestException('Invalid dateOfBirth format');
          }
          updateData.dateOfBirth = dateOfBirth;
        } else {
          updateData.dateOfBirth = null;
        }
      }

      // 4. Update the employee record
      await employee.update(updateData as any, { transaction });

      // 5. Update department information if provided
      if (request.departmentInfo) {
        // Find existing employee department record
        let employeeDepartment = await this.employeeDepartmentModel.findOne({
          where: { userId: employee.userId },
          transaction,
        });

        if (employeeDepartment) {
          // Update existing record
          const departmentUpdateData: any = {};
          if (request.departmentInfo.employeeId !== undefined)
            departmentUpdateData.employeeId = request.departmentInfo.employeeId;
          if (request.departmentInfo.department !== undefined)
            departmentUpdateData.department = request.departmentInfo.department;
          if (request.departmentInfo.designation !== undefined)
            departmentUpdateData.designation =
              request.departmentInfo.designation;
          if (request.departmentInfo.supervisor !== undefined)
            departmentUpdateData.supervisor = request.departmentInfo.supervisor;
          if (request.departmentInfo.workLocation !== undefined)
            departmentUpdateData.workLocation =
              request.departmentInfo.workLocation;

          // If department name is being updated, find/create the department
          if (request.departmentInfo.department !== undefined) {
            const organization = await this.organizationModel.findByPk(
              (employee as any).user?.organizationId || 1,
              { transaction }
            );

            if (organization) {
              let department = await this.departmentModel.findOne({
                where: {
                  name: request.departmentInfo.department,
                  organizationId: organization.id,
                },
                transaction,
              });

              if (!department) {
                department = await this.departmentModel.create(
                  {
                    name: request.departmentInfo.department,
                    organizationId: organization.id,
                    description: `${request.departmentInfo.department} department`,
                  },
                  { transaction }
                );
              }

              departmentUpdateData.departmentId = department.id;
            }
          }

          await employeeDepartment.update(departmentUpdateData, {
            transaction,
          });
        } else if (request.departmentInfo.department) {
          // Create new department record if it doesn't exist
          const organization = await this.organizationModel.findByPk(
            (employee as any).user?.organizationId || 1,
            { transaction }
          );

          if (organization) {
            let department = await this.departmentModel.findOne({
              where: {
                name: request.departmentInfo.department,
                organizationId: organization.id,
              },
              transaction,
            });

            if (!department) {
              department = await this.departmentModel.create(
                {
                  name: request.departmentInfo.department,
                  organizationId: organization.id,
                  description: `${request.departmentInfo.department} department`,
                },
                { transaction }
              );
            }

            await this.employeeDepartmentModel.create(
              {
                userId: employee.userId,
                departmentId: department.id,
                employeeId: request.departmentInfo.employeeId,
                department: request.departmentInfo.department,
                designation: request.departmentInfo.designation,
                supervisor: request.departmentInfo.supervisor,
                workLocation: request.departmentInfo.workLocation,
              },
              { transaction }
            );
          }
        }
      }

      await transaction.commit();

      // 6. Handle related data updates (addresses, contacts, etc.) if provided
      await this.updateRelatedEmployeeRecords(BigInt(employee.userId), request);

      // 7. Audit log
      await this.createAuditLog({
        userId: Number(request.requestUserId),
        userRole: request.roleName,
        actions: 'UPDATE_EMPLOYEE',
        serviceName: 'auth-service',
        resourceType: 'Employee',
        resourceId: Number(employee.userId),
        description: `Updated employee: ${employee.userId}`,
        ipAddress: request.ipAddress,
        userAgent: request.userAgent,
        source: 'grpc',
      });

      this.logger.log(`Employee updated successfully: ${employee.userId}`);

      // 8. Fetch the updated employee with all relations
      const updatedEmployee = await this.employeeModel.findByPk(
        employee.userId,
        {
          include: [
            { model: this.employeeAddressModel, as: 'addresses' },
            {
              model: this.employeeEmergencyContactModel,
              as: 'emergencyContacts',
            },
            { model: this.employeeIdentityDocModel, as: 'identityDocs' },
            { model: this.employeeBankAccountModel, as: 'bankAccounts' },
            { model: this.employeeSocialLinksModel, as: 'socialLinks' },
            {
              model: this.employeeDepartmentModel,
              as: 'employeeDepartment',
              include: [{ model: this.departmentModel, as: 'departmentRef' }],
            },
          ],
        }
      );

      // Metrics
      this.trackMetrics('update_employee', 'success', Date.now() - startTime);

      return {
        success: true,
        message: 'Employee updated successfully',
        employee: await this.mapToEmployeeInfo(updatedEmployee),
      };
    } catch (error) {
      // Rollback transaction if it exists and hasn't been committed
      if (transaction) {
        try {
          await transaction.rollback();
        } catch (rollbackError) {
          this.logger.error('Failed to rollback transaction', rollbackError);
        }
      }

      this.logger.error(
        `Error updating employee: ${error.message}`,
        error.stack
      );

      // Metrics
      this.trackMetrics('update_employee', 'error', Date.now() - startTime);

      return {
        success: false,
        message: error.message || 'Failed to update employee',
      };
    }
  }

  async findEmployeeByOrganization(
    req: ListEmployeesRequest
  ): Promise<EmployeePersonal[]> {
    try {
      const page = req.page || 1;
      const limit = req.limit || 10;
      const offset = (page - 1) * limit;

      const whereClause: any = {};
      if (req.organizationId) {
        whereClause.orgId = req.organizationId;
      }

      const { rows: employees, count: total } =
        await this.employeeModel.findAndCountAll({
          where: whereClause,
          include: [
            { model: this.employeeAddressModel, as: 'addresses' },
            {
              model: this.employeeEmergencyContactModel,
              as: 'emergencyContacts',
            },
            { model: this.employeeIdentityDocModel, as: 'identityDocs' },
            { model: this.employeeBankAccountModel, as: 'bankAccounts' },
            { model: this.employeeSocialLinksModel, as: 'socialLinks' },
            {
              model: this.employeeDepartmentModel,
              as: 'employeeDepartment',
              include: [{ model: this.departmentModel, as: 'departmentRef' }],
            },
          ],
          limit,
          offset,
          order: [['firstName', 'ASC']],
        });

      return employees;
    } catch (error) {
      this.logger.error(
        `Error finding employees: ${error.message}`,
        error.stack
      );
      throw new Error('Failed to find employees');
    }
  }

  // Legacy methods remain unchanged
  async create(data: CreateEmployeeDto): Promise<EmployeePersonal> {
    return this.employeeModel.create(data as any);
  }

  async findAll(): Promise<EmployeePersonal[]> {
    return this.employeeModel.findAll({
      include: [
        { model: this.employeeAddressModel, as: 'addresses' },
        { model: this.employeeEmergencyContactModel, as: 'emergencyContacts' },
        { model: this.employeeIdentityDocModel, as: 'identityDocs' },
        { model: this.employeeBankAccountModel, as: 'bankAccounts' },
        { model: this.employeeSocialLinksModel, as: 'socialLinks' },
        {
          model: this.employeeDepartmentModel,
          as: 'employeeDepartment',
          include: [{ model: this.departmentModel, as: 'departmentRef' }],
        },
      ],
    });
  }

  async findOne(id: number): Promise<EmployeePersonal> {
    const emp = await this.employeeModel.findByPk(id, {
      include: [
        { model: this.employeeAddressModel, as: 'addresses' },
        { model: this.employeeEmergencyContactModel, as: 'emergencyContacts' },
        { model: this.employeeIdentityDocModel, as: 'identityDocs' },
        { model: this.employeeBankAccountModel, as: 'bankAccounts' },
        { model: this.employeeSocialLinksModel, as: 'socialLinks' },
        {
          model: this.employeeDepartmentModel,
          as: 'employeeDepartment',
          include: [{ model: this.departmentModel, as: 'departmentRef' }],
        },
      ],
    });
    if (!emp) throw new NotFoundException(`Employee ${id} not found`);
    return emp;
  }

  async update(id: number, data: UpdateEmployeeDto): Promise<EmployeePersonal> {
    const emp = await this.findOne(id);
    await emp.update(data as any);
    return emp;
  }

  async remove(id: number): Promise<{ deleted: boolean }> {
    const emp = await this.findOne(id);
    await emp.destroy();
    return { deleted: true };
  }
}
