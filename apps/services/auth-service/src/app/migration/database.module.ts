import { Module, OnModuleInit } from '@nestjs/common';
import { SequelizeModule } from '@nestjs/sequelize';
import { authDatabaseConfig } from './database.config';
import { User } from '../user/model/user.model';
import { UserRole } from '../user/model/user-role.model';
import { SocialSite } from '../user/model/social-site.model';
import { Token } from '../auth/token.model';
import { Feature } from '../user/model/feature.model';
import { SubFeature } from '../user/model/sub-feature.model';
import { Module as ModuleModel } from '../user/model/module.model';
import { Role } from '../user/model/role.model';
import { RoleFeaturePermission } from '../user/model/role-feature-permission.model';
import { RoleSubFeaturePermission } from '../user/model/role-sub-feature-permission.model';
import { Sequelize } from 'sequelize-typescript';
import { Department } from '../user/model/department.model';
import { UserDepartment } from '../user/model/user‐department.model';
import { DepartmentFeaturePermission } from '../user/model/department-feature-permission.model';
import { DepartmentSubFeaturePermission } from '../user/model/department-sub-feature-permission.model';
import { EmployeePersonal } from '../employee/models/employee-personal.model';
import { EmployeeAddress } from '../employee/models/employee-address.model';
import { EmployeeEmergencyContact } from '../employee/models/employee-emergency-contact.model';
import { EmployeeIdentityDoc } from '../employee/models/employee-identity-doc.model';
import { EmployeeBankAccount } from '../employee/models/employee-bank-account.model';
import { Organization } from '../organization/organization.model';
import { EmployeeSocialLinks } from '../employee/models/employee-social-links.model';
import { EmployeeDepartment } from '../employee/models/employee-department.model';

const models = [
  Feature,
  SubFeature,
  Department,
  UserDepartment,
  DepartmentFeaturePermission,
  DepartmentSubFeaturePermission,
  ModuleModel, // Module
  Role,
  RoleFeaturePermission,
  RoleSubFeaturePermission,
  User,
  UserRole,
  Token,
  SocialSite,
  EmployeePersonal,
  EmployeeAddress,
  EmployeeEmergencyContact,
  EmployeeIdentityDoc,
  EmployeeBankAccount,
  EmployeeSocialLinks,
  EmployeeDepartment,
  Organization,
];

@Module({
  imports: [
    SequelizeModule.forRoot({
      ...authDatabaseConfig,
      models,
      autoLoadModels: true,
      synchronize: false, // We're using migrations, so set this to false
    }),
    SequelizeModule.forFeature(models),
  ],
  exports: [SequelizeModule],
})
export class DatabaseModule implements OnModuleInit {
  constructor(private readonly sequelize: Sequelize) {}
  onModuleInit() {
    Role.initScopes();
  }
}
